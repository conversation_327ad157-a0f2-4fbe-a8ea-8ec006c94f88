from odoo import models, api
# import logging

# _logger = logging.getLogger(__name__)

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    def action_view_pending_po_moves(self):
        self.ensure_one()
        location_ptn_vendor = self.env.ref("stock.stock_location_suppliers")

        # 使用标准的 stock.move.line action
        action = self.env["ir.actions.actions"]._for_xml_id("stock.stock_move_line_action")

        # 修改为 stock.move.line 的 domain
        action['domain'] = [
            ('move_id.picking_type_id.code', '=', 'incoming'),
            ('move_id.state', 'in', ['waiting', 'confirmed', 'partially_available', 'assigned']),
            ('move_id.location_id', '=', location_ptn_vendor.id),
            ('product_id.product_tmpl_id', '=', self.id),
        ]

        # 设置 context，保持原始的 context 设置
        action['context'] = {
            'create': 0,
            'pivot_measures': ['quantity_product_uom', '__count__'],
            'search_default_product_id': self.product_variant_ids[0].id if self.product_variant_ids else False,
        }

        # 指定使用独立的收货专用视图，确保显示正确的字段
        action['views'] = [
            (self.env.ref('stock_move_line_ui_enhancement_panfu.view_move_line_tree_receipt').id, 'tree'),
            (False, 'form')
        ]

        # 设置名称
        action['name'] = 'Pending PO Move Lines'
        action['target'] = 'current'

        return action

    def get_moves(self, product_ids, extra_domain):
        """
        Override get_moves to include extended status states for PO Qty calculation.

        This method extends the original filtering to include 'waiting' status moves,
        addressing the business requirement to show 'Waiting Availability' status
        in PO quantity calculations.

        Args:
            product_ids (list): List of product template IDs
            extra_domain (list): Additional domain filters (currently ignored for consistency)

        Returns:
            list: Read group results with product quantities
        """
        if not product_ids:
            return []

        # Build optimized domain for purchase order moves
        domain = [
            ("picking_code", "=", "incoming"),
            ("location_id.usage", "=", "supplier"),
            ("product_id.product_tmpl_id", "in", product_ids),
            ("state", "in", ["waiting", "confirmed", "partially_available", "assigned"]),
        ]

        # Note: extra_domain is intentionally ignored to maintain consistent
        # behavior with the enhanced status filtering requirements

        result = self.env["stock.move"].read_group(
            domain,
            ["product_uom_qty", "reserved_availability"],
            ["product_id"]
        )

        return result